@import "tailwindcss";

@font-face {
  font-family: 'Woodblock';
  src: url('/woodblock.otf') format('opentype');
  font-display: swap;
}

@font-face {
  font-family: 'Futura';
  src: url('/Futura.woff2') format('woff2'),
      url('/Futura.woff') format('woff'),
      url('/Futura.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

/* Base styles */
html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Futura', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  font-optical-sizing: auto;
  font-style: normal;
  @apply bg-slate-950 text-slate-100 antialiased;
}

/* Typography */
.woodblock {
  font-family: 'Woodblock', sans-serif;
}

/* Form elements */
.input {
  @apply bg-slate-800/80 text-slate-100 border-slate-600/50 rounded-lg p-3 outline-none transition-all border focus:border-yellow-400 focus:ring-2 focus:ring-yellow-400/20 placeholder-slate-400;
}

/* Container utility */
.container {
  @apply max-w-7xl mx-auto;
}

/* Accessibility utilities */
.sr-only {
  @apply absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap border-0;
}

.focus\:not-sr-only:focus {
  @apply static w-auto h-auto p-4 m-0 overflow-visible whitespace-normal;
}

/* Custom hover states */
.hover-lift {
  @apply transition-transform duration-200 hover:-translate-y-1;
}

/* Gradient text utility */
.gradient-text {
  @apply bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text text-transparent;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-slate-900;
}

::-webkit-scrollbar-thumb {
  @apply bg-slate-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-slate-500;
}

/* Focus styles for better accessibility */
button:focus-visible,
a:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible {
  @apply outline-2 outline-offset-2 outline-yellow-400;
}

/* Animation utilities */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

.animate-pulse-slow {
  animation: pulse 2s ease-in-out infinite;
}

/* Enhanced visual elements */
.glass-effect {
  @apply bg-slate-800/40 backdrop-blur-lg border border-slate-700/60 shadow-xl;
}

.section-divider {
  @apply border-t border-slate-700/30 pt-4 mt-4;
}

/* Improved preset button styling */
.preset-button {
  @apply w-10 h-10 rounded-lg cursor-pointer hover:-translate-y-0.5 transition-all border-2 flex items-center justify-center shadow-sm;
}

.preset-button.selected {
  @apply border-yellow-400 shadow-yellow-400/20;
}

.preset-button:not(.selected) {
  @apply border-slate-600/50 hover:border-slate-500;
}

.icon-accent {
  @apply text-yellow-400;
}

.card-hover {
  @apply transition-all duration-200 hover:shadow-lg hover:shadow-slate-900/20 hover:-translate-y-0.5;
}

/* Layout height balancing */
.editor-grid {
  @apply grid lg:grid-cols-3 gap-8 max-w-7xl mx-auto items-stretch;
  min-height: 600px; /* Ensure minimum height for consistency */
}

.preview-container {
  @apply lg:col-span-2 order-2 lg:order-1 flex flex-col;
  min-height: 0; /* Allow shrinking */
}

.toolbar-container {
  @apply lg:col-span-1 order-1 lg:order-2 flex flex-col;
  min-height: 0; /* Allow shrinking */
}

.sticky-content {
  @apply sticky flex-1 flex flex-col;
  top: 2rem; /* 32px equivalent to top-8 */
}

.toolbar-sticky {
  @apply min-h-0; /* Allow shrinking when needed */
}

/* Responsive design improvements */
@media (max-width: 1024px) {
  .container {
    @apply px-6;
  }

  .editor-grid {
    min-height: auto; /* Remove min-height on mobile */
  }

  .sticky-content {
    position: static; /* Disable sticky on mobile */
  }
}

@media (max-width: 768px) {
  .container {
    @apply px-4;
  }

  /* 移动端工具栏优化 */
  .mobile-toolbar {
    @apply max-h-none overflow-visible;
  }

  .mobile-toolbar .flex-1 {
    flex: none; /* Disable flex-1 on mobile for better content flow */
  }

  /* 移动端预设主题网格 */
  .mobile-preset-grid {
    @apply grid-cols-1;
  }

  /* 移动端颜色选择器网格 - 更紧凑的布局 */
  .mobile-color-grid {
    @apply grid-cols-3;
  }

  /* 移动端预设按钮优化 */
  .preset-button {
    width: 2rem;
    height: 2rem;
  }

  /* 移动端输入框优化 */
  .input {
    padding: 0.625rem;
    font-size: 1rem;
  }
}

@media (max-width: 640px) {
  /* 超小屏幕优化 */
  .mobile-color-grid {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}